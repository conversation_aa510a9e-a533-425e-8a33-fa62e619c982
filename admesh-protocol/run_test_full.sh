#!/bin/bash

# AdMesh Full Test Environment Runner
# Runs both backend (production Firebase + localhost API) and frontend (test mode) in sync

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 AdMesh Full Test Environment Setup${NC}"
echo -e "${BLUE}======================================${NC}"
echo ""

# Check if we're in the right directory
if [ ! -f "api/main.py" ]; then
    echo -e "${RED}❌ Error: Must be run from admesh-protocol directory${NC}"
    echo "   Current directory: $(pwd)"
    echo "   Expected files: api/main.py, firebase/serviceAccountKey.json"
    exit 1
fi

# Check if dashboard directory exists
if [ ! -d "../admesh-dashboard" ]; then
    echo -e "${RED}❌ Error: admesh-dashboard directory not found${NC}"
    echo "   Expected location: ../admesh-dashboard"
    exit 1
fi

# Verify production Firebase credentials
echo -e "${YELLOW}🔍 Verifying Firebase configuration...${NC}"
if [ ! -f "./firebase/serviceAccountKey.json" ]; then
    echo -e "${RED}❌ Error: Production Firebase credentials not found!${NC}"
    echo "   Please ensure ./firebase/serviceAccountKey.json exists"
    exit 1
fi

if grep -q "admesh-9560c" "./firebase/serviceAccountKey.json"; then
    echo -e "${GREEN}   ✅ Production Firebase project (admesh-9560c) confirmed${NC}"
else
    echo -e "${RED}   ❌ Error: serviceAccountKey.json does not contain production project ID${NC}"
    exit 1
fi

# Load test environment variables
if [ -f ".env.test" ]; then
    echo -e "${YELLOW}📋 Loading backend test environment variables...${NC}"
    export $(cat .env.test | grep -v '^#' | xargs)
else
    echo -e "${RED}❌ Error: .env.test file not found in backend!${NC}"
    exit 1
fi

# Set environment variables
export ENV=test
export ENVIRONMENT=test
export HOST=0.0.0.0
if [ -z "$PORT" ]; then
    export PORT=8000
fi

# Check dashboard environment file
if [ ! -f "../admesh-dashboard/.env.test" ]; then
    echo -e "${RED}❌ Error: .env.test file not found in dashboard!${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}✅ Configuration verified:${NC}"
echo -e "   Backend Environment: ${ENV}"
echo -e "   Backend API: http://127.0.0.1:${PORT}"
echo -e "   Backend Firebase: admesh-9560c (production)"
echo -e "   Frontend Environment: test"
echo -e "   Frontend URL: http://localhost:3000"
echo -e "   Frontend API calls: http://127.0.0.1:${PORT}"
echo ""

# Function to cleanup background processes
cleanup() {
    echo ""
    echo -e "${YELLOW}🛑 Shutting down services...${NC}"
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        echo -e "${GREEN}   ✅ Backend stopped${NC}"
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        echo -e "${GREEN}   ✅ Frontend stopped${NC}"
    fi
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Check for virtual environment (optional)
if [ -d "venv" ]; then
    echo -e "${YELLOW}🐍 Activating virtual environment...${NC}"
    source venv/bin/activate
elif [ -d "testenv" ]; then
    echo -e "${YELLOW}🐍 Activating test virtual environment...${NC}"
    source testenv/bin/activate
else
    echo -e "${GREEN}🐍 Using current Python environment ($(python3 --version))${NC}"
fi

# Install backend dependencies
echo -e "${YELLOW}📦 Installing backend dependencies...${NC}"
pip install -r requirements.txt > /dev/null 2>&1

# Check if dashboard dependencies are installed
echo -e "${YELLOW}📦 Checking dashboard dependencies...${NC}"
cd ../admesh-dashboard
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}   Installing dashboard dependencies...${NC}"
    npm install > /dev/null 2>&1
fi
cd ../admesh-protocol

echo ""
echo -e "${BLUE}🚀 Starting services...${NC}"
echo ""

# Start backend
echo -e "${YELLOW}🔧 Starting backend (admesh-protocol)...${NC}"
uvicorn api.main:app \
    --host $HOST \
    --port $PORT \
    --reload \
    --log-level info \
    --env-file .env.test &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Start frontend
echo -e "${YELLOW}🎨 Starting frontend (admesh-dashboard)...${NC}"
cd ../admesh-dashboard
NEXT_PUBLIC_ENVIRONMENT=test npm run dev:test &
FRONTEND_PID=$!
cd ../admesh-protocol

echo ""
echo -e "${GREEN}✅ Both services started successfully!${NC}"
echo ""
echo -e "${BLUE}📋 Service Information:${NC}"
echo -e "   🔧 Backend API: ${GREEN}http://127.0.0.1:${PORT}${NC}"
echo -e "   🎨 Frontend: ${GREEN}http://localhost:3000${NC}"
echo -e "   📊 API Docs: ${GREEN}http://127.0.0.1:${PORT}/docs${NC}"
echo -e "   🔥 Firebase: ${GREEN}admesh-9560c (production)${NC}"
echo ""
echo -e "${YELLOW}📝 Press Ctrl+C to stop both services${NC}"
echo ""

# Wait for both processes
wait $BACKEND_PID $FRONTEND_PID
