# AdMesh Test Environment Usage Guide

The test environment allows you to run AdMesh with **production Firebase/database** while using **localhost API endpoints** for development and testing.

## 🎯 Purpose

- **Backend**: Uses production Firebase service account (`firebase/serviceAccountKey.json`) connecting to `admesh-9560c`
- **Frontend**: Uses production Firebase client config but calls localhost API (`http://127.0.0.1:8000`)
- **Data**: All data comes from production database
- **Testing**: Safe local development against real data

## 🚀 Quick Start

### Option 1: Run Both Services Together (Recommended)

```bash
cd admesh-protocol
./run_test_full.sh
```

This will start both backend and frontend in sync with proper configuration.

### Option 2: Run Services Separately

**Terminal 1 - Backend:**
```bash
cd admesh-protocol
./run_test.sh
# OR manually:
ENV=test uvicorn api.main:app --host 127.0.0.1 --port 8000 --reload
```

**Terminal 2 - Frontend:**
```bash
cd admesh-dashboard
./run_test.sh
# OR manually:
npm run dev:test
```

## 📋 Configuration Details

### Backend Configuration
- **Environment**: `ENV=test`
- **Firebase**: Production service account (`firebase/serviceAccountKey.json`)
- **Project**: `admesh-9560c` (production)
- **API**: `http://127.0.0.1:8000`
- **Config File**: `.env.test`

### Frontend Configuration
- **Environment**: `NEXT_PUBLIC_ENVIRONMENT=test`
- **Firebase**: Production client config (`admesh-9560c`)
- **API Calls**: `http://127.0.0.1:8000`
- **Frontend**: `http://localhost:3000`
- **Config File**: `.env.test`

## 🔍 Verification

When running correctly, you should see:

**Backend Logs:**
```
🚀 Starting AdMesh Protocol API in test environment
📦 Using project ID: admesh-9560c
✅ Loaded credentials from: ./firebase/serviceAccountKey.json
✅ Firebase initialized successfully
```

**Frontend Logs:**
```
🔥 Initializing Firebase with config: {
  environment: 'test',
  projectId: 'admesh-9560c',
  authDomain: 'admesh-9560c.firebaseapp.com'
}
```

## 🌐 Access Points

- **Frontend Dashboard**: http://localhost:3000
- **Backend API**: http://127.0.0.1:8000
- **API Documentation**: http://127.0.0.1:8000/docs
- **Firebase Project**: admesh-9560c (production)

## ⚠️ Important Notes

1. **Production Data**: You're working with real production data - be careful!
2. **Firebase Credentials**: Ensure `firebase/serviceAccountKey.json` exists and contains production credentials
3. **Sync**: Both services must be running for full functionality
4. **Authentication**: Use production user accounts to sign in
5. **API Calls**: All frontend API calls go to localhost, not production API

## 🛠️ Troubleshooting

### Backend Issues
- Check `firebase/serviceAccountKey.json` exists
- Verify `.env.test` file is present
- Ensure `ENV=test` is set
- Check port 8000 is available

### Frontend Issues
- Check `.env.test` file exists in dashboard
- Verify `NEXT_PUBLIC_ENVIRONMENT=test` is set
- Ensure backend is running on port 8000
- Check node_modules are installed

### Common Errors
- **Firebase 401**: Check service account credentials
- **CORS errors**: Verify backend CORS configuration includes localhost:3000
- **Connection refused**: Ensure backend is running before starting frontend

## 📁 File Structure

```
admesh-protocol/
├── firebase/
│   ├── serviceAccountKey.json      # Production credentials
│   └── dev-serviceAccountKey.json  # Development credentials
├── .env.test                       # Backend test config
├── run_test.sh                     # Backend test runner
└── run_test_full.sh               # Full environment runner

admesh-dashboard/
├── .env.test                       # Frontend test config
└── run_test.sh                     # Frontend test runner
```

## 🔄 Environment Switching

- **Development**: Uses `admesh-dev` project
- **Test**: Uses `admesh-9560c` project with localhost API
- **Production**: Uses `admesh-9560c` project with production API

The test environment gives you the best of both worlds: production data with local development flexibility!
