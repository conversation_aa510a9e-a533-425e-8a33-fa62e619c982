#!/bin/bash

# AdMesh Dashboard - Test Environment Runner
# Uses production Firebase but calls localhost API

echo "🧪 Starting AdMesh Dashboard in TEST mode..."
echo "🔥 Firebase: Production (admesh-9560c)"
echo "🌐 API: http://127.0.0.1:8000"
echo "📱 Dashboard: http://localhost:3000"
echo "🔧 Environment: test"
echo ""

# Check if test environment file exists
if [ ! -f ".env.test" ]; then
    echo "❌ Error: .env.test file not found!"
    echo "   Please ensure .env.test exists in the dashboard directory"
    exit 1
fi

# Load test environment variables
echo "📋 Loading test environment variables..."
export $(cat .env.test | grep -v '^#' | xargs)

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
else
    echo "📦 Dependencies already installed"
fi

# Set environment to test
export NEXT_PUBLIC_ENVIRONMENT=test

echo "🚀 Starting Next.js development server..."
echo "   Environment: $NEXT_PUBLIC_ENVIRONMENT"
echo "   Firebase Project: $NEXT_PUBLIC_FIREBASE_PROJECT_ID"
echo "   API URL: $NEXT_PUBLIC_API_BASE_URL"
echo "   Dashboard URL: http://localhost:3000"
echo ""
echo "📝 Logs will show below..."
echo "   Press Ctrl+C to stop the server"
echo ""

# Start the development server with test environment
npm run dev:test
